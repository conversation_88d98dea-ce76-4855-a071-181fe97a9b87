## AGENT GUIDELINES

This document outlines the conventions and commands for agentic coding in this repository.

### 1. Build, Lint, and Test Commands

- **Build**: `pnpm build`
- **Lint**: `pnpm lint`
- **Run Development Server**: `pnpm dev`

No dedicated test commands or test files were found. It is recommended to establish a testing framework and add corresponding scripts.

### 2. Code Style Guidelines

- **Formatting**: Adhere to ESLint rules as configured in `eslint.config.js`.
- **Typing**: Use TypeScript for all new and existing code, ensuring strong typing.
- **Naming Conventions**: Follow standard JavaScript/TypeScript conventions (e.g., camelCase for variables/functions, PascalCase for components/types).
- **Imports**: Organize imports consistently, typically grouped by external libraries, then internal modules, with a blank line separation.
- **Error Handling**: Implement robust error handling using try-catch blocks for asynchronous operations and clear error messages.

### 3. Agent-Specific Rules

No specific Cursor or Copilot rules were found in the repository.
