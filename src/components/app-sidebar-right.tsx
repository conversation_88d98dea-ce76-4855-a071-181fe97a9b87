import * as React from "react"
import {
  <PERSON><PERSON><PERSON>,
  User,
  Bell,
  HelpCircle,
  Command,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
} from "@/components/ui/sidebar"

const rightSidebarData = {
  header: {
    title: "Control Panel",
    subtitle: "Admin",
  },
  sections: [
    {
      title: "Quick Actions",
      items: [
        {
          title: "User Settings",
          icon: User,
          url: "#",
        },
        {
          title: "Notifications",
          icon: Bell,
          url: "#",
        },
        {
          title: "System Settings",
          icon: Settings,
          url: "#",
        },
      ],
    },
    {
      title: "Support",
      items: [
        {
          title: "Help Center",
          icon: HelpCircle,
          url: "#",
        },
      ],
    },
  ],
}

export function AppSidebarRight({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar side="right" variant="inset" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton size="lg">
              <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
                <Command className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">{rightSidebarData.header.title}</span>
                <span className="truncate text-xs">{rightSidebarData.header.subtitle}</span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        {rightSidebarData.sections.map((section) => (
          <SidebarGroup key={section.title}>
            <SidebarGroupLabel>{section.title}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {section.items.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton asChild>
                      <a href={item.url}>
                        <item.icon />
                        <span>{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
    </Sidebar>
  )
}
