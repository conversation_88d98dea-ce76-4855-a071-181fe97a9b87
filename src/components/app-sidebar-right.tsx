import * as React from "react"
import {
  Command,
} from "lucide-react"

import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
} from "@/components/ui/sidebar"

const rightSidebarData = {
  header: {
    title: "Control Panel",
    subtitle: "Admin",
  },
}

export function AppSidebarRight({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar side="right" variant="inset" {...props}>
      <SidebarHeader>
        <div className="flex items-center gap-2 px-2 py-2">
          <div className="bg-sidebar-primary text-sidebar-primary-foreground flex aspect-square size-8 items-center justify-center rounded-lg">
            <Command className="size-4" />
          </div>
          <div className="grid flex-1 text-left text-sm leading-tight">
            <span className="truncate font-medium">{rightSidebarData.header.title}</span>
            <span className="truncate text-xs">{rightSidebarData.header.subtitle}</span>
          </div>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarGroup>
          <SidebarGroupLabel>Voter Details</SidebarGroupLabel>
          <SidebarGroupContent>
            <div className="px-2 py-4 text-sm text-muted-foreground">
              Voter information will be displayed here.
            </div>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
