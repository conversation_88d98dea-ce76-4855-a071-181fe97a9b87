import * as React from "react"

export type SidebarContextProps = {
  state: "expanded" | "collapsed"
  open: boolean
  setOpen: (open: boolean) => void
  openMobile: boolean
  setOpenMobile: (open: boolean) => void
  isMobile: boolean
  toggleSidebar: () => void
  // Right sidebar state
  rightState: "expanded" | "collapsed"
  rightOpen: boolean
  setRightOpen: (open: boolean) => void
  rightOpenMobile: boolean
  setRightOpenMobile: (open: boolean) => void
  toggleRightSidebar: () => void
}

export const SidebarContext = React.createContext<SidebarContextProps | null>(null)

export function useSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useSidebar must be used within a SidebarProvider.")
  }

  return context
}

// Hook specifically for right sidebar
export function useRightSidebar() {
  const context = React.useContext(SidebarContext)
  if (!context) {
    throw new Error("useRightSidebar must be used within a SidebarProvider.")
  }

  return {
    state: context.rightState,
    open: context.rightOpen,
    setOpen: context.setRightOpen,
    openMobile: context.rightOpenMobile,
    setOpenMobile: context.setRightOpenMobile,
    isMobile: context.isMobile,
    toggleSidebar: context.toggleRightSidebar,
  }
}
